#!/usr/bin/env python3
"""
Advanced Pattern Recognition Engine
Performs historical pattern analysis and generates advanced trading signals
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from config import ADVANCED_PATTERN_CONFIG, TRADING_CONFIG
from utils import print_colored, fetch_live_candles

class AdvancedPatternEngine:
    def __init__(self):
        """Initialize the advanced pattern recognition engine"""
        self.config = ADVANCED_PATTERN_CONFIG
        self.scoring_weights = self.config['SCORING_WEIGHTS']
        
    def analyze_time_pattern(self, pair, target_time, analysis_days, start_time=None, end_time=None):
        """
        Analyze historical pattern for a specific time across multiple days
        
        Args:
            pair: Currency pair (e.g., 'EUR_USD')
            target_time: Time to analyze (e.g., '19:34')
            analysis_days: Number of days to look back
            start_time: Start of trading session (optional)
            end_time: End of trading session (optional)
        
        Returns:
            dict: Pattern analysis results with signal and confidence
        """
        try:
            # Fetch historical data with reasonable limits (Oanda API limits)
            # Request maximum 5000 candles (about 3.5 days of 1-minute data)
            max_candles = min(5000, analysis_days * 1000)  # Reasonable limit
            df = fetch_live_candles(pair, max_candles)

            if df is None or len(df) < 500:  # Need at least 500 candles for analysis
                return self._create_no_signal_result("Insufficient data")
            
            # Add technical indicators
            df = self._add_technical_indicators(df)
            
            # Filter data by session time if specified
            if start_time and end_time:
                df = self._filter_by_session_time(df, start_time, end_time)
            
            # Find candles at target time for each day
            pattern_candles = self._extract_time_pattern_candles(df, target_time, analysis_days)
            
            if len(pattern_candles) < 3:
                return self._create_no_signal_result("Insufficient pattern data")
            
            # Analyze pattern consistency
            pattern_analysis = self._analyze_pattern_consistency(pattern_candles)
            
            # Apply advanced filters
            current_candle = df.iloc[-1]
            filter_scores = self._apply_advanced_filters(df, current_candle, pattern_analysis)
            
            # Calculate final signal and confidence
            final_result = self._calculate_final_signal(pattern_analysis, filter_scores, pair, target_time)
            
            return final_result
            
        except Exception as e:
            print_colored(f"❌ Error in pattern analysis for {pair}: {str(e)}", "ERROR")
            return self._create_no_signal_result(f"Analysis error: {str(e)}")
    
    def _add_technical_indicators(self, df):
        """Add technical indicators to dataframe using pandas calculations"""
        try:
            # Ensure we have OHLCV columns
            if not all(col in df.columns for col in ['open', 'high', 'low', 'close', 'volume']):
                print_colored("❌ Missing required OHLCV columns", "ERROR")
                return df

            # Moving averages
            df['ema_20'] = df['close'].ewm(span=20).mean()
            df['ema_50'] = df['close'].ewm(span=50).mean()

            # RSI calculation
            df['rsi'] = self._calculate_rsi(df['close'], 14)

            # MACD calculation
            macd_data = self._calculate_macd(df['close'])
            df['macd'] = macd_data['macd']
            df['macd_signal'] = macd_data['signal']
            df['macd_hist'] = macd_data['histogram']

            # ATR for volatility
            df['atr'] = self._calculate_atr(df, 14)

            # Bollinger Bands
            bb_data = self._calculate_bollinger_bands(df['close'], 20, 2)
            df['bb_upper'] = bb_data['upper']
            df['bb_middle'] = bb_data['middle']
            df['bb_lower'] = bb_data['lower']

            return df

        except Exception as e:
            print_colored(f"❌ Error adding technical indicators: {str(e)}", "ERROR")
            return df

    def _calculate_rsi(self, prices, period=14):
        """Calculate RSI using pandas"""
        try:
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            return rsi
        except:
            return pd.Series([50] * len(prices), index=prices.index)

    def _calculate_macd(self, prices, fast=12, slow=26, signal=9):
        """Calculate MACD using pandas"""
        try:
            ema_fast = prices.ewm(span=fast).mean()
            ema_slow = prices.ewm(span=slow).mean()
            macd = ema_fast - ema_slow
            macd_signal = macd.ewm(span=signal).mean()
            macd_histogram = macd - macd_signal

            return {
                'macd': macd,
                'signal': macd_signal,
                'histogram': macd_histogram
            }
        except:
            return {
                'macd': pd.Series([0] * len(prices), index=prices.index),
                'signal': pd.Series([0] * len(prices), index=prices.index),
                'histogram': pd.Series([0] * len(prices), index=prices.index)
            }

    def _calculate_atr(self, df, period=14):
        """Calculate ATR using pandas"""
        try:
            high_low = df['high'] - df['low']
            high_close = np.abs(df['high'] - df['close'].shift())
            low_close = np.abs(df['low'] - df['close'].shift())

            ranges = pd.concat([high_low, high_close, low_close], axis=1)
            true_range = ranges.max(axis=1)
            atr = true_range.rolling(window=period).mean()

            return atr
        except:
            return pd.Series([0.001] * len(df), index=df.index)

    def _calculate_bollinger_bands(self, prices, period=20, std_dev=2):
        """Calculate Bollinger Bands using pandas"""
        try:
            middle = prices.rolling(window=period).mean()
            std = prices.rolling(window=period).std()
            upper = middle + (std * std_dev)
            lower = middle - (std * std_dev)

            return {
                'upper': upper,
                'middle': middle,
                'lower': lower
            }
        except:
            return {
                'upper': pd.Series([0] * len(prices), index=prices.index),
                'middle': pd.Series([0] * len(prices), index=prices.index),
                'lower': pd.Series([0] * len(prices), index=prices.index)
            }
    
    def _filter_by_session_time(self, df, start_time, end_time):
        """Filter dataframe by trading session time"""
        try:
            # Convert time strings to datetime.time objects
            start_time_obj = datetime.strptime(start_time, self.config['TIME_FORMATS']['INPUT_FORMAT']).time()
            end_time_obj = datetime.strptime(end_time, self.config['TIME_FORMATS']['INPUT_FORMAT']).time()

            # Extract time from datetime index
            df['time_only'] = pd.to_datetime(df.index).time

            print_colored(f"   📊 Original data: {len(df)} candles", "INFO")
            print_colored(f"   ⏰ Session filter: {start_time} to {end_time}", "INFO")

            # Filter by time range
            if start_time_obj <= end_time_obj:
                # Same day range
                mask = (df['time_only'] >= start_time_obj) & (df['time_only'] <= end_time_obj)
            else:
                # Overnight range (e.g., 22:00 to 06:00)
                mask = (df['time_only'] >= start_time_obj) | (df['time_only'] <= end_time_obj)

            filtered_df = df[mask].copy()
            print_colored(f"   📊 After session filter: {len(filtered_df)} candles", "INFO")

            # If no data after filtering, return original data with warning
            if len(filtered_df) == 0:
                print_colored(f"   ⚠️ No data in session time range, using all data", "WARNING")
                filtered_df = df.copy()

            if 'time_only' in filtered_df.columns:
                filtered_df.drop('time_only', axis=1, inplace=True)

            return filtered_df

        except Exception as e:
            print_colored(f"❌ Error filtering by session time: {str(e)}", "ERROR")
            return df
    
    def _extract_time_pattern_candles(self, df, target_time, analysis_days):
        """Extract candles at specific time for pattern analysis"""
        try:
            target_time_obj = datetime.strptime(target_time, self.config['TIME_FORMATS']['INPUT_FORMAT']).time()
            
            # Group by date and find candles closest to target time
            df['date'] = pd.to_datetime(df.index).date
            df['time_only'] = pd.to_datetime(df.index).time
            
            pattern_candles = []
            
            # Get unique dates (most recent first)
            unique_dates = sorted(df['date'].unique(), reverse=True)
            
            for date in unique_dates[:analysis_days]:
                day_data = df[df['date'] == date]
                
                if len(day_data) == 0:
                    continue
                
                # Find candle closest to target time
                day_data['time_diff'] = day_data['time_only'].apply(
                    lambda x: abs((datetime.combine(datetime.today(), x) - 
                                 datetime.combine(datetime.today(), target_time_obj)).total_seconds())
                )
                
                closest_candle = day_data.loc[day_data['time_diff'].idxmin()]
                pattern_candles.append(closest_candle)
            
            return pattern_candles
            
        except Exception as e:
            print_colored(f"❌ Error extracting pattern candles: {str(e)}", "ERROR")
            return []
    
    def _analyze_pattern_consistency(self, pattern_candles):
        """Analyze consistency of pattern across days"""
        try:
            if len(pattern_candles) == 0:
                return {'signal': 'HOLD', 'consistency': 0.0, 'pattern_type': 'NONE'}
            
            # Analyze candle directions
            directions = []
            strong_candles = 0
            
            for candle in pattern_candles:
                # Determine candle direction
                if candle['close'] > candle['open']:
                    directions.append('BUY')
                elif candle['close'] < candle['open']:
                    directions.append('SELL')
                else:
                    directions.append('NEUTRAL')
                
                # Check candle strength
                body_size = abs(candle['close'] - candle['open'])
                total_range = candle['high'] - candle['low']
                
                if total_range > 0 and (body_size / total_range) >= self.config['MIN_CANDLE_BODY_RATIO']:
                    strong_candles += 1
            
            # Calculate consistency
            if len(directions) == 0:
                return {'signal': 'HOLD', 'consistency': 0.0, 'pattern_type': 'NONE'}
            
            buy_count = directions.count('BUY')
            sell_count = directions.count('SELL')
            total_count = len(directions)
            
            # Determine dominant pattern
            if buy_count > sell_count:
                consistency = buy_count / total_count
                pattern_signal = 'BUY'
                pattern_display = f"{buy_count}/{total_count}"
            elif sell_count > buy_count:
                consistency = sell_count / total_count
                pattern_signal = 'SELL'
                pattern_display = f"{sell_count}/{total_count}"
            else:
                consistency = 0.0
                pattern_signal = 'HOLD'
                pattern_display = f"0/{total_count}"

            return {
                'signal': pattern_signal,
                'consistency': consistency,
                'pattern_type': pattern_display,
                'strong_candles_ratio': strong_candles / total_count if total_count > 0 else 0,
                'total_days': total_count,
                'buy_days': buy_count,
                'sell_days': sell_count,
                'pattern_details': f"{buy_count} BUY, {sell_count} SELL out of {total_count} days"
            }
            
        except Exception as e:
            print_colored(f"❌ Error analyzing pattern consistency: {str(e)}", "ERROR")
            return {'signal': 'HOLD', 'consistency': 0.0, 'pattern_type': 'ERROR'}
    
    def _apply_advanced_filters(self, df, current_candle, pattern_analysis):
        """Apply advanced filters for signal validation"""
        try:
            scores = {}
            
            # 1. Trend Alignment Filter
            scores['trend'] = self._calculate_trend_score(df, pattern_analysis['signal'])
            
            # 2. Market Structure Filter
            scores['structure'] = self._calculate_structure_score(df, current_candle, pattern_analysis['signal'])
            
            # 3. Volatility Filter
            scores['volatility'] = self._calculate_volatility_score(df, current_candle)
            
            # 4. Momentum Filter (RSI/MACD)
            scores['momentum'] = self._calculate_momentum_score(current_candle, pattern_analysis['signal'])
            
            # 5. Candle Strength Filter
            scores['candle_strength'] = pattern_analysis['strong_candles_ratio']
            
            return scores
            
        except Exception as e:
            print_colored(f"❌ Error applying advanced filters: {str(e)}", "ERROR")
            return {'trend': 0, 'structure': 0, 'volatility': 0, 'momentum': 0, 'candle_strength': 0}
    
    def _calculate_trend_score(self, df, signal):
        """Calculate trend alignment score"""
        try:
            if len(df) < self.config['TREND_LOOKBACK']:
                return 0.0
            
            recent_data = df.tail(self.config['TREND_LOOKBACK'])
            
            # EMA trend
            ema_20 = recent_data['ema_20'].iloc[-1]
            ema_50 = recent_data['ema_50'].iloc[-1]
            current_price = recent_data['close'].iloc[-1]
            
            score = 0.0
            
            if signal == 'BUY':
                # Relaxed bullish trend conditions
                if current_price > ema_20:  # Removed ema_20 > ema_50 requirement
                    score += 0.4
                if recent_data['close'].iloc[-1] > recent_data['close'].iloc[-10]:
                    score += 0.3
                if recent_data['high'].iloc[-1] >= recent_data['high'].iloc[-5]:  # Changed > to >=
                    score += 0.3
                # Additional lenient condition
                if current_price > recent_data['close'].iloc[-5]:
                    score += 0.2

            elif signal == 'SELL':
                # Relaxed bearish trend conditions
                if current_price < ema_20:  # Removed ema_20 < ema_50 requirement
                    score += 0.4
                if recent_data['close'].iloc[-1] < recent_data['close'].iloc[-10]:
                    score += 0.3
                if recent_data['low'].iloc[-1] <= recent_data['low'].iloc[-5]:  # Changed < to <=
                    score += 0.3
                # Additional lenient condition
                if current_price < recent_data['close'].iloc[-5]:
                    score += 0.2
            
            return min(1.0, score)
            
        except Exception as e:
            print_colored(f"❌ Error calculating trend score: {str(e)}", "ERROR")
            return 0.0
    
    def _calculate_structure_score(self, df, current_candle, signal):
        """Calculate market structure score"""
        try:
            if len(df) < self.config['STRUCTURE_LOOKBACK']:
                return 0.0
            
            recent_data = df.tail(self.config['STRUCTURE_LOOKBACK'])
            current_price = current_candle['close']
            
            # Find support and resistance levels
            highs = recent_data['high'].nlargest(5).values
            lows = recent_data['low'].nsmallest(5).values
            
            score = 0.0
            
            if signal == 'BUY':
                # Relaxed: Check if price is near support (wider range)
                for support in lows:
                    if abs(current_price - support) / current_price < 0.005:  # Within 0.5% (was 0.1%)
                        score += 0.5
                        break

                # Relaxed: Check if price is above recent lows
                if current_price > min(lows):
                    score += 0.3

                # Additional lenient condition
                if current_price >= recent_data['low'].iloc[-10:].mean():
                    score += 0.2

            elif signal == 'SELL':
                # Relaxed: Check if price is near resistance (wider range)
                for resistance in highs:
                    if abs(current_price - resistance) / current_price < 0.005:  # Within 0.5% (was 0.1%)
                        score += 0.5
                        break

                # Relaxed: Check if price is below recent highs
                if current_price < max(highs):
                    score += 0.3

                # Additional lenient condition
                if current_price <= recent_data['high'].iloc[-10:].mean():
                    score += 0.2
            
            return min(1.0, score)
            
        except Exception as e:
            print_colored(f"❌ Error calculating structure score: {str(e)}", "ERROR")
            return 0.0
    
    def _calculate_volatility_score(self, df, current_candle):
        """Calculate volatility filter score"""
        try:
            if 'atr' not in df.columns or pd.isna(current_candle['atr']):
                return 0.5  # Neutral score if ATR not available
            
            current_atr = current_candle['atr']
            avg_atr = df['atr'].tail(20).mean()
            
            # Score based on volatility level
            if current_atr >= avg_atr * self.config['VOLATILITY_THRESHOLD']:
                return 1.0  # High volatility - good for trading
            elif current_atr >= avg_atr * 0.8:
                return 0.7  # Moderate volatility
            else:
                return 0.3  # Low volatility - less favorable
                
        except Exception as e:
            print_colored(f"❌ Error calculating volatility score: {str(e)}", "ERROR")
            return 0.5
    
    def _calculate_momentum_score(self, current_candle, signal):
        """Calculate momentum filter score using RSI and MACD"""
        try:
            score = 0.0
            
            # Relaxed RSI momentum
            if 'rsi' in current_candle and not pd.isna(current_candle['rsi']):
                rsi = current_candle['rsi']

                if signal == 'BUY':
                    if rsi < self.config['RSI_OVERSOLD']:  # Strong oversold
                        score += 0.5
                    elif rsi < 50:  # Below midline (relaxed)
                        score += 0.3
                    else:  # Any RSI for BUY (very relaxed)
                        score += 0.1

                elif signal == 'SELL':
                    if rsi > self.config['RSI_OVERBOUGHT']:  # Strong overbought
                        score += 0.5
                    elif rsi > 50:  # Above midline (relaxed)
                        score += 0.3
                    else:  # Any RSI for SELL (very relaxed)
                        score += 0.1
            
            # Relaxed MACD momentum
            if all(col in current_candle and not pd.isna(current_candle[col])
                   for col in ['macd', 'macd_signal']):
                macd_diff = current_candle['macd'] - current_candle['macd_signal']

                if signal == 'BUY':
                    if macd_diff > self.config['MACD_SIGNAL_THRESHOLD']:  # Strong bullish
                        score += 0.5
                    elif macd_diff > 0:  # Any positive MACD (relaxed)
                        score += 0.3
                    else:  # Even negative MACD gets some score (very relaxed)
                        score += 0.1

                elif signal == 'SELL':
                    if macd_diff < -self.config['MACD_SIGNAL_THRESHOLD']:  # Strong bearish
                        score += 0.5
                    elif macd_diff < 0:  # Any negative MACD (relaxed)
                        score += 0.3
                    else:  # Even positive MACD gets some score (very relaxed)
                        score += 0.1
            
            return min(1.0, score)
            
        except Exception as e:
            print_colored(f"❌ Error calculating momentum score: {str(e)}", "ERROR")
            return 0.0
    
    def _calculate_final_signal(self, pattern_analysis, filter_scores, pair, target_time):
        """Calculate final signal and confidence score"""
        try:
            # Base pattern score
            pattern_score = pattern_analysis['consistency'] * (self.scoring_weights['PATTERN_CONSISTENCY'] / 100)
            
            # Filter scores
            trend_score = filter_scores['trend'] * (self.scoring_weights['TREND_ALIGNMENT'] / 100)
            structure_score = filter_scores['structure'] * (self.scoring_weights['STRUCTURE_SUPPORT'] / 100)
            volatility_score = filter_scores['volatility'] * (self.scoring_weights['VOLATILITY_FILTER'] / 100)
            momentum_score = filter_scores['momentum'] * (self.scoring_weights['MOMENTUM_FILTER'] / 100)
            candle_score = filter_scores['candle_strength'] * (self.scoring_weights['CANDLE_STRENGTH'] / 100)
            
            # Calculate total confidence
            total_confidence = pattern_score + trend_score + structure_score + volatility_score + momentum_score + candle_score
            
            # Determine final signal (relaxed thresholds)
            min_confidence_threshold = 0.4  # Relaxed from TRADING_CONFIG['MIN_CONFIDENCE']
            if (pattern_analysis['consistency'] >= self.config['MIN_PATTERN_CONSISTENCY'] and
                total_confidence >= min_confidence_threshold):
                final_signal = pattern_analysis['signal']
            else:
                final_signal = 'HOLD'
                total_confidence = 0.0
            
            return {
                'signal': final_signal,
                'confidence': min(0.95, total_confidence),  # Cap at 95%
                'pair': pair,
                'target_time': target_time,
                'pattern_analysis': pattern_analysis,
                'filter_scores': filter_scores,
                'score_breakdown': {
                    'pattern': pattern_score,
                    'trend': trend_score,
                    'structure': structure_score,
                    'volatility': volatility_score,
                    'momentum': momentum_score,
                    'candle_strength': candle_score
                }
            }
            
        except Exception as e:
            print_colored(f"❌ Error calculating final signal: {str(e)}", "ERROR")
            return self._create_no_signal_result("Calculation error")
    
    def _create_no_signal_result(self, reason):
        """Create a no-signal result with reason"""
        return {
            'signal': 'HOLD',
            'confidence': 0.0,
            'pair': '',
            'target_time': '',
            'reason': reason,
            'pattern_analysis': {'signal': 'HOLD', 'consistency': 0.0, 'pattern_type': 'NONE'},
            'filter_scores': {'trend': 0, 'structure': 0, 'volatility': 0, 'momentum': 0, 'candle_strength': 0}
        }
