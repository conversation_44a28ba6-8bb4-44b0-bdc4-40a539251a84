# 🎯 Advanced Pattern Recognition Trading Bot

## 📋 Overview

The Advanced Pattern Recognition Trading Bot is a sophisticated binary trading system that generates signals by analyzing historical patterns and applying advanced filtering techniques. It identifies repeating patterns at specific times across multiple days and validates them using trend analysis, market structure, and momentum indicators.

## 🚀 Key Features

### 🔍 Historical Pattern Recognition
- **Time-Based Analysis**: Analyzes candle directions at specific times across multiple days
- **Pattern Consistency**: Identifies repeating patterns (e.g., 5/5 red candles at 19:34)
- **Candle Strength Filtering**: Only counts candles with significant body-to-range ratios
- **Session Time Filtering**: Optional analysis within specific trading hours

### 🎯 Advanced Filtering System
1. **Trend Confirmation** (20% weight)
   - EMA crossovers (20/50 periods)
   - Price action analysis
   - Higher highs/lows detection

2. **Market Structure Analysis** (15% weight)
   - Support/resistance level identification
   - Breakout/breakdown confirmation
   - Order block detection

3. **Volatility Filter** (10% weight)
   - ATR-based volatility measurement
   - Market activity validation
   - Optimal trading conditions

4. **Momentum Filter** (10% weight)
   - RSI overbought/oversold conditions
   - MACD signal line crossovers
   - Momentum alignment

5. **Candle Strength Filter** (5% weight)
   - Body-to-total range ratio
   - Wick analysis
   - Signal quality assessment

### 📊 Intelligent Scoring System
- **Pattern Consistency**: 40% weight (base pattern score)
- **Multi-factor Validation**: 60% weight (advanced filters)
- **Confidence Threshold**: Configurable minimum confidence (default: 60%)
- **Score Breakdown**: Detailed analysis of each component

## 💱 Supported Currency Pairs

The system uses the same 10 major currency pairs as your existing trading bots:
- EUR_USD, GBP_USD, USD_JPY, AUD_USD, USD_CAD
- AUD_CAD, EUR_JPY, GBP_JPY, USD_CHF, EUR_GBP

## 🎨 User Interface Integration

### Main Menu Integration
The Advanced Pattern Signals option is integrated into your existing trading bot launcher in **3 places**:

1. **Main Menu**: Option 3 - "🎯 Advanced Pattern Signals"
2. **Backtesting Menu**: Option 3 - "🎯 Advanced Pattern Backtesting"
3. **Live Analysis**: Direct integration with existing signal display system

### Color-Coded Display
- **🟢 BUY Signals**: Green with confidence percentage
- **🔴 SELL Signals**: Red with confidence percentage
- **⚪ HOLD/No Signal**: Gray when no pattern meets criteria
- **📊 Score Breakdown**: Individual filter scores displayed

## 📈 How to Use

### 1. Launch the System
```bash
python trading_bot_launcher.py
```

### 2. Select Advanced Pattern Signals
Choose option **3** from the main menu: "🎯 Advanced Pattern Signals"

### 3. Choose Analysis Type
- **Single Time Analysis**: Analyze pattern for one specific time
- **Full Day Pattern Scan**: Scan ALL candles for consistent patterns across the entire day

### 4. Select Currency Pairs
- Choose specific pairs or select "all" for all 10 pairs
- Same selection interface as existing trading bots

### 5. Configure Analysis Parameters

#### For Single Time Analysis:
- **Analysis Days**: Number of historical days to analyze (3-30)
- **Target Time**: Specific time to analyze (HH:MM format, e.g., 19:34)
- **Session Times**: Optional start/end times for session filtering

#### For Full Day Pattern Scan:
- **Analysis Days**: Number of historical days to analyze (3-10)
- **Minimum Confidence**: Threshold for displaying opportunities (50-95%)
- **Session Times**: Optional start/end times to limit scanning period
- **Automatic Scanning**: System checks all candle times for patterns

### 6. Review Results
The system displays a comprehensive table with:
- Currency pair
- Signal direction (BUY/SELL/HOLD)
- Overall confidence percentage
- Pattern consistency
- Individual filter scores

## 📊 Example Output

### Single Time Analysis:
```
📅 TRADING DATE: 2025-06-04 (Wednesday)

💱 PAIR         | 🕐 TIME   | 📈📉 SIGNAL  | 🎯 CONF% | 📊 PATTERN | 🔄 TREND | 🏗️ STRUCT | 📈 VOLAT | ⚡ MOMENT | 💪 STRENGTH
EUR_USD         | 19:34     | 📈 BUY       | 🎯 78.5% | 📊 4/5     | 🔄 85%   | 🏗️ 70%   | 📈 90%   | ⚡ 60%   | 💪 80%
GBP_USD         | 19:34     | 📉 SELL      | 🎯 82.1% | 📊 5/5     | 🔄 90%   | 🏗️ 85%   | 📈 75%   | ⚡ 95%   | 💪 100%
USD_JPY         | 19:34     | ⚪ HOLD      | 🎯 -     | 📊 2/5     | 🔄 45%   | 🏗️ 30%   | 📈 60%   | ⚡ 40%   | 💪 40%
```

### Full Day Pattern Scan:
```
🎯 PATTERN TRADING OPPORTUNITIES FOUND
✅ Found 12 trading opportunities!
📊 Analysis Period: 5 days | Min Confidence: 70%

📅 TRADE DATE    | 📆 DAY       | 💱 PAIR    | 🕐 TIME   | 📈📉 SIGNAL  | 🎯 CONF% | 📊 PATTERN | 🔄 TREND | 🏗️ STRUCT | 📈 VOLAT | ⚡ MOMENT
2025-06-04       | Wednesday    | EUR_USD    | 08:30     | 📈 BUY       | 85.2%    | 5/5        | 90%      | 80%       | 85%      | 75%
2025-06-04       | Wednesday    | GBP_USD    | 14:15     | 📉 SELL      | 82.7%    | 4/5        | 85%      | 85%       | 80%      | 80%
2025-06-04       | Wednesday    | USD_JPY    | 19:45     | 📈 BUY       | 78.9%    | 4/5        | 75%      | 70%       | 90%      | 85%
2025-06-04       | Wednesday    | AUD_USD    | 02:30     | 📉 SELL      | 76.4%    | 4/5        | 80%      | 65%       | 75%      | 70%
2025-06-04       | Wednesday    | EUR_GBP    | 16:00     | 📈 BUY       | 74.1%    | 3/5        | 70%      | 75%       | 80%      | 75%

📊 OPPORTUNITY SUMMARY:
   📅 Trading Date: 2025-06-04 (Wednesday)
   📈 BUY Opportunities: 7
   📉 SELL Opportunities: 5
   🎯 Average Confidence: 79.2%

🏆 TOP 3 OPPORTUNITIES FOR 2025-06-04:
   1. 📈 EUR_USD at 08:30 on Wednesday - 85.2% confidence
   2. 📉 GBP_USD at 14:15 on Wednesday - 82.7% confidence
   3. 📈 USD_JPY at 19:45 on Wednesday - 78.9% confidence

💡 TRADING PLAN FOR 2025-06-04 (Wednesday):
   📅 Set alerts for the specified times on Wednesday
   🕐 Monitor these exact times for the pattern signals
   🎯 Higher confidence = stronger historical pattern
   ⚠️ Always consider current market conditions
   📊 Pattern based on 5 days of historical data
```

## ⚙️ Configuration

### Pattern Recognition Settings
```python
ADVANCED_PATTERN_CONFIG = {
    "DEFAULT_ANALYSIS_DAYS": 5,           # Default lookback period
    "MIN_PATTERN_CONSISTENCY": 0.6,       # 60% consistency required
    "MIN_CANDLE_BODY_RATIO": 0.3,        # Body must be 30% of range
    "VOLATILITY_THRESHOLD": 1.2,          # ATR multiplier
    "RSI_OVERBOUGHT": 70,                 # RSI upper threshold
    "RSI_OVERSOLD": 30,                   # RSI lower threshold
}
```

### Scoring Weights
```python
"SCORING_WEIGHTS": {
    "PATTERN_CONSISTENCY": 40,    # Base pattern score
    "TREND_ALIGNMENT": 20,        # Trend confirmation
    "STRUCTURE_SUPPORT": 15,      # Support/resistance
    "VOLATILITY_FILTER": 10,      # Volatility confirmation
    "MOMENTUM_FILTER": 10,        # RSI/MACD
    "CANDLE_STRENGTH": 5          # Candle quality
}
```

## 🔧 Technical Implementation

### Core Components

1. **AdvancedPatternEngine** (`advanced_pattern_engine.py`)
   - Pattern recognition logic
   - Technical indicator calculations
   - Advanced filtering system
   - Signal generation and scoring

2. **AdvancedSignalsBot** (`advanced_signals_bot.py`)
   - User interface and interaction
   - Parameter setup and validation
   - Results display and formatting
   - Integration with existing systems

3. **Configuration** (`config.py`)
   - Pattern recognition parameters
   - Scoring weights and thresholds
   - Display and formatting settings

### Technical Indicators (Pandas-based)
- **EMA**: Exponential Moving Averages (20, 50 periods)
- **RSI**: Relative Strength Index (14 periods)
- **MACD**: Moving Average Convergence Divergence
- **ATR**: Average True Range (14 periods)
- **Bollinger Bands**: 20-period with 2 standard deviations

## 📊 Example Usage Scenarios

### Scenario 1: London Session Breakout
```
Analysis Days: 5
Target Time: 08:30
Session: 08:00 - 12:00
```
Analyzes if EUR_USD consistently breaks higher/lower at 8:30 AM during London open.

### Scenario 2: New York Close Pattern
```
Analysis Days: 7
Target Time: 16:55
Session: 16:00 - 17:00
```
Identifies patterns in the final minutes before New York close.

### Scenario 3: Asian Session Reversal
```
Analysis Days: 10
Target Time: 02:30
Session: All day
```
Looks for reversal patterns during quiet Asian trading hours.

## 🎯 Signal Interpretation

### High Confidence Signals (80%+)
- Strong pattern consistency (4/5 or 5/5 days)
- Multiple filter confirmations
- Clear trend alignment
- Good market structure support

### Medium Confidence Signals (60-79%)
- Moderate pattern consistency (3/5 days)
- Some filter confirmations
- Mixed trend/structure signals

### Low Confidence/No Signal (<60%)
- Weak pattern consistency
- Conflicting filter signals
- Sideways market conditions

## 🔄 Integration with Existing Systems

The Advanced Pattern Recognition system seamlessly integrates with your existing trading infrastructure:

- **Same Currency Pairs**: Uses your established 10-pair selection
- **Consistent UI**: Matches color scheme and display format
- **Menu Integration**: Added to main launcher in 3 locations
- **API Compatibility**: Uses same Oanda API connection
- **Display Format**: Consistent with rule-based and ML signal tables

## 🚧 Future Enhancements

1. **Multiple Time Analysis**: Analyze several times simultaneously
2. **Pattern Backtesting**: Historical validation of pattern strategies
3. **Live Monitoring**: Real-time pattern detection and alerts
4. **Pattern Strength Scoring**: Enhanced pattern quality metrics
5. **Custom Pattern Definition**: User-defined pattern criteria

## 📞 Support and Troubleshooting

### Common Issues
1. **No Signals Generated**: Check minimum confidence threshold
2. **API Connection**: Ensure Oanda API credentials are valid
3. **Data Availability**: Verify sufficient historical data exists

### Testing the System
Test the system by running the main launcher:
```bash
python trading_bot_launcher.py
```
Then select option 3 for Advanced Pattern Signals.

This comprehensive system provides sophisticated pattern recognition capabilities while maintaining the familiar interface and reliability of your existing trading bot infrastructure.
