#!/usr/bin/env python3
"""
Advanced Signals Trading Bot
Generates advanced trading signals using historical pattern recognition
"""

import time
import threading
from datetime import datetime, timedelta
import pandas as pd
from advanced_pattern_engine import AdvancedPatternEngine
from utils import (
    print_colored, print_header, select_currency_pairs,
    get_current_time_info, format_price
)
from config import CURRENCY_PAIRS, ADVANCED_PATTERN_CONFIG

class AdvancedSignalsBot:
    def __init__(self, selected_pairs=None):
        """Initialize the advanced signals bot"""
        self.pattern_engine = AdvancedPatternEngine()
        self.pairs = selected_pairs if selected_pairs else CURRENCY_PAIRS.copy()
        self.analysis_params = {}
        
    def setup_analysis_parameters(self):
        """Setup analysis parameters from user input"""
        print_header("🔧 ADVANCED PATTERN ANALYSIS SETUP")
        
        try:
            # Get analysis days
            while True:
                try:
                    days_input = input(f"📅 Number of days to analyze (default: {ADVANCED_PATTERN_CONFIG['DEFAULT_ANALYSIS_DAYS']}): ").strip()
                    if not days_input:
                        self.analysis_params['days'] = ADVANCED_PATTERN_CONFIG['DEFAULT_ANALYSIS_DAYS']
                        break
                    else:
                        days = int(days_input)
                        if 3 <= days <= 30:
                            self.analysis_params['days'] = days
                            break
                        else:
                            print_colored("❌ Please enter a number between 3 and 30", "ERROR")
                except ValueError:
                    print_colored("❌ Please enter a valid number", "ERROR")
            
            # Get target time
            while True:
                try:
                    time_input = input("🕐 Target time to analyze (HH:MM format, e.g., 19:34): ").strip()
                    if time_input:
                        # Validate time format
                        datetime.strptime(time_input, ADVANCED_PATTERN_CONFIG['TIME_FORMATS']['INPUT_FORMAT'])
                        self.analysis_params['target_time'] = time_input
                        break
                    else:
                        print_colored("❌ Please enter a valid time in HH:MM format", "ERROR")
                except ValueError:
                    print_colored("❌ Invalid time format. Please use HH:MM (e.g., 19:34)", "ERROR")
            
            # Get session time range (optional)
            print_colored("\n⏰ Trading session time range (optional - press Enter to skip):", "INFO")
            
            start_time_input = input("🌅 Session start time (HH:MM): ").strip()
            end_time_input = input("🌇 Session end time (HH:MM): ").strip()
            
            if start_time_input and end_time_input:
                try:
                    # Validate time formats
                    datetime.strptime(start_time_input, ADVANCED_PATTERN_CONFIG['TIME_FORMATS']['INPUT_FORMAT'])
                    datetime.strptime(end_time_input, ADVANCED_PATTERN_CONFIG['TIME_FORMATS']['INPUT_FORMAT'])
                    self.analysis_params['start_time'] = start_time_input
                    self.analysis_params['end_time'] = end_time_input
                    print_colored(f"✅ Session time set: {start_time_input} - {end_time_input}", "SUCCESS")
                except ValueError:
                    print_colored("⚠️ Invalid session times, will analyze all day", "WARNING")
                    self.analysis_params['start_time'] = None
                    self.analysis_params['end_time'] = None
            else:
                self.analysis_params['start_time'] = None
                self.analysis_params['end_time'] = None
                print_colored("ℹ️ No session time specified, will analyze all day", "INFO")
            
            # Display setup summary
            print_colored("\n📋 Analysis Setup Summary:", "HEADER", bold=True)
            print_colored(f"   📅 Analysis Days: {self.analysis_params['days']}", "INFO")
            print_colored(f"   🕐 Target Time: {self.analysis_params['target_time']}", "INFO")
            if self.analysis_params['start_time'] and self.analysis_params['end_time']:
                print_colored(f"   ⏰ Session: {self.analysis_params['start_time']} - {self.analysis_params['end_time']}", "INFO")
            else:
                print_colored(f"   ⏰ Session: All day", "INFO")
            print_colored(f"   💱 Currency Pairs: {len(self.pairs)} pairs", "INFO")
            print()
            
            return True
            
        except Exception as e:
            print_colored(f"❌ Error setting up analysis parameters: {str(e)}", "ERROR")
            return False
    
    def analyze_patterns(self):
        """Analyze patterns for all selected currency pairs"""
        print_header(f"🔍 ADVANCED PATTERN ANALYSIS - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Print analysis header
        self._print_analysis_header()
        
        signals_found = 0
        total_pairs = len(self.pairs)
        
        for i, pair in enumerate(self.pairs, 1):
            try:
                print_colored(f"📊 Analyzing {pair} ({i}/{total_pairs})...", "INFO")
                
                # Perform pattern analysis
                result = self.pattern_engine.analyze_time_pattern(
                    pair=pair,
                    target_time=self.analysis_params['target_time'],
                    analysis_days=self.analysis_params['days'],
                    start_time=self.analysis_params.get('start_time'),
                    end_time=self.analysis_params.get('end_time')
                )
                
                # Display result
                self._display_analysis_result(pair, result)
                
                if result['signal'] != 'HOLD':
                    signals_found += 1
                
                # Small delay to avoid overwhelming the API
                time.sleep(0.5)
                
            except Exception as e:
                print_colored(f"❌ Error analyzing {pair}: {str(e)}", "ERROR")
                self._display_error_result(pair, str(e))
        
        # Display summary
        self._display_analysis_summary(signals_found, total_pairs)
    
    def _print_analysis_header(self):
        """Print the analysis results table header"""
        header_line = (
            f"💱 {'PAIR':<12} | "
            f"📈📉 {'SIGNAL':<8} | "
            f"🎯 {'CONF%':<6} | "
            f"📊 {'PATTERN':<8} | "
            f"🔄 {'TREND':<6} | "
            f"🏗️ {'STRUCT':<6} | "
            f"📈 {'VOLAT':<6} | "
            f"⚡ {'MOMENT':<6} | "
            f"💪 {'STRENGTH':<8}"
        )
        
        separator_line = "=" * 100
        
        print_colored(separator_line, "HEADER")
        print_colored(header_line, "HEADER", bold=True)
        print_colored(separator_line, "HEADER")
    
    def _display_analysis_result(self, pair, result):
        """Display analysis result for a single pair"""
        try:
            # Prepare display values
            signal = result['signal']
            confidence = result['confidence']
            pattern_info = result.get('pattern_analysis', {})
            filter_scores = result.get('filter_scores', {})
            
            # Format values
            if signal == 'HOLD':
                signal_display = "⚪ HOLD"
                signal_color = "NO_SIGNAL"
                conf_display = "-"
            elif signal == 'BUY':
                signal_display = "📈 BUY"
                signal_color = "BUY"
                conf_display = f"{confidence*100:.1f}%"
            else:  # SELL
                signal_display = "📉 SELL"
                signal_color = "SELL"
                conf_display = f"{confidence*100:.1f}%"
            
            # Pattern info
            pattern_display = pattern_info.get('pattern_type', '-')
            
            # Filter scores (convert to percentages)
            trend_score = f"{filter_scores.get('trend', 0)*100:.0f}%"
            struct_score = f"{filter_scores.get('structure', 0)*100:.0f}%"
            volat_score = f"{filter_scores.get('volatility', 0)*100:.0f}%"
            moment_score = f"{filter_scores.get('momentum', 0)*100:.0f}%"
            strength_score = f"{filter_scores.get('candle_strength', 0)*100:.0f}%"
            
            # Create the formatted row
            row_line = (
                f"💱 {pair:<12} | "
                f"{signal_display:<12} | "
                f"🎯 {conf_display:<6} | "
                f"📊 {pattern_display:<8} | "
                f"🔄 {trend_score:<6} | "
                f"🏗️ {struct_score:<6} | "
                f"📈 {volat_score:<6} | "
                f"⚡ {moment_score:<6} | "
                f"💪 {strength_score:<8}"
            )
            
            # Print with appropriate color
            print_colored(row_line, signal_color)
            
        except Exception as e:
            print_colored(f"❌ Error displaying result for {pair}: {str(e)}", "ERROR")
    
    def _display_error_result(self, pair, error_msg):
        """Display error result for a pair"""
        row_line = (
            f"💱 {pair:<12} | "
            f"❌ ERROR   | "
            f"🎯 -     | "
            f"📊 -       | "
            f"🔄 -     | "
            f"🏗️ -     | "
            f"📈 -     | "
            f"⚡ -     | "
            f"💪 -       "
        )
        print_colored(row_line, "ERROR")
    
    def _display_analysis_summary(self, signals_found, total_pairs):
        """Display analysis summary"""
        print_colored("=" * 100, "HEADER")
        print_colored(f"📊 ANALYSIS COMPLETE", "HEADER", bold=True)
        print_colored(f"   🎯 Signals Found: {signals_found}/{total_pairs} pairs", "SUCCESS" if signals_found > 0 else "INFO")
        print_colored(f"   📅 Analysis Period: {self.analysis_params['days']} days", "INFO")
        print_colored(f"   🕐 Target Time: {self.analysis_params['target_time']}", "INFO")
        
        if signals_found > 0:
            print_colored(f"   ✅ {signals_found} trading opportunities identified!", "SUCCESS", bold=True)
        else:
            print_colored(f"   ⚠️ No signals met the minimum confidence threshold", "WARNING")
        
        print_colored("=" * 100, "HEADER")

def get_user_analysis_choice():
    """Get user choice for analysis type"""
    print_header("🔍 ADVANCED PATTERN ANALYSIS OPTIONS")
    print_colored("Choose analysis type:", "INFO", bold=True)
    print()
    print_colored("1. 🎯 Single Time Analysis", "BUY", bold=True)
    print_colored("   • Analyze pattern for one specific time", "INFO")
    print_colored("   • Best for testing specific trading times", "INFO")
    print()
    print_colored("2. 📊 Full Day Pattern Scan", "SUCCESS", bold=True)
    print_colored("   • Scan ALL candles for consistent patterns", "SUCCESS")
    print_colored("   • Find multiple trading opportunities", "SUCCESS")
    print_colored("   • Complete list with times, directions & confidence", "SUCCESS")
    print()
    print_colored("3. ⬅️  Back to Main Menu", "WARNING", bold=True)
    print()

    while True:
        try:
            choice = input("Enter your choice (1-3): ").strip()
            if choice in ['1', '2', '3']:
                return choice
            else:
                print_colored("❌ Invalid choice. Please enter 1-3.", "ERROR")
        except KeyboardInterrupt:
            print_colored("\n⚠️ Returning to main menu...", "WARNING")
            return '3'

def start_full_day_pattern_scan(selected_pairs=None):
    """Start full day pattern scanning for all times"""
    try:
        print_header("📊 FULL DAY PATTERN SCAN")
        print_colored("🔍 Scanning ALL candles for consistent patterns", "HEADER")
        print_colored("📈 This will find multiple trading opportunities throughout the day", "HEADER")
        print()

        # Get analysis parameters
        while True:
            try:
                days_input = input(f"📅 Number of days to analyze (default: 3, max: 5): ").strip()
                if not days_input:
                    analysis_days = 3  # Reduced default for API limits
                    break
                else:
                    days = int(days_input)
                    if 3 <= days <= 5:  # Reduced max for API limits
                        analysis_days = days
                        break
                    else:
                        print_colored("❌ Please enter a number between 3 and 5", "ERROR")
            except ValueError:
                print_colored("❌ Please enter a valid number", "ERROR")

        # Get session time range (optional)
        print_colored("\n⏰ Trading session time range (optional - press Enter to skip):", "INFO")
        start_time_input = input("🌅 Session start time (HH:MM): ").strip()
        end_time_input = input("🌇 Session end time (HH:MM): ").strip()

        if start_time_input and end_time_input:
            try:
                from config import ADVANCED_PATTERN_CONFIG
                datetime.strptime(start_time_input, ADVANCED_PATTERN_CONFIG['TIME_FORMATS']['INPUT_FORMAT'])
                datetime.strptime(end_time_input, ADVANCED_PATTERN_CONFIG['TIME_FORMATS']['INPUT_FORMAT'])
                session_start = start_time_input
                session_end = end_time_input
                print_colored(f"✅ Session time set: {start_time_input} - {end_time_input}", "SUCCESS")
            except ValueError:
                print_colored("⚠️ Invalid session times, will analyze all day", "WARNING")
                session_start = None
                session_end = None
        else:
            session_start = None
            session_end = None
            print_colored("ℹ️ No session time specified, will analyze all day", "INFO")

        # Get minimum confidence threshold
        while True:
            try:
                conf_input = input(f"\n🎯 Minimum confidence threshold (default: 70%): ").strip()
                if not conf_input:
                    min_confidence = 0.70
                    break
                else:
                    conf = float(conf_input.replace('%', '')) / 100
                    if 0.5 <= conf <= 0.95:
                        min_confidence = conf
                        break
                    else:
                        print_colored("❌ Please enter a percentage between 50% and 95%", "ERROR")
            except ValueError:
                print_colored("❌ Please enter a valid percentage", "ERROR")

        print_colored(f"\n📋 Full Day Scan Parameters:", "HEADER", bold=True)
        print_colored(f"   📅 Analysis Days: {analysis_days}", "INFO")
        print_colored(f"   🎯 Min Confidence: {min_confidence*100:.0f}%", "INFO")
        if session_start and session_end:
            print_colored(f"   ⏰ Session: {session_start} - {session_end}", "INFO")
        else:
            print_colored(f"   ⏰ Session: All day", "INFO")
        print_colored(f"   💱 Currency Pairs: {len(selected_pairs)} pairs", "INFO")
        print()

        # Perform full day scan
        perform_full_day_scan(selected_pairs, analysis_days, session_start, session_end, min_confidence)

    except Exception as e:
        print_colored(f"❌ Error in full day pattern scan: {str(e)}", "ERROR")

def perform_full_day_scan(pairs, analysis_days, session_start, session_end, min_confidence):
    """Perform comprehensive pattern scan for all times"""
    try:
        from advanced_pattern_engine import AdvancedPatternEngine

        print_header(f"🔍 SCANNING {len(pairs)} PAIRS FOR PATTERN OPPORTUNITIES")

        engine = AdvancedPatternEngine()
        all_opportunities = []

        for pair_idx, pair in enumerate(pairs, 1):
            print_colored(f"\n📊 Analyzing {pair} ({pair_idx}/{len(pairs)})...", "INFO", bold=True)

            try:
                # Fetch live data for this pair with reasonable limits
                max_candles = min(5000, analysis_days * 1000)  # Oanda API limit
                from utils import fetch_live_candles
                df = fetch_live_candles(pair, max_candles)

                if df is None or len(df) < 500:
                    print_colored(f"   ❌ Insufficient data for {pair}", "ERROR")
                    continue

                # Add technical indicators
                df = engine._add_technical_indicators(df)

                # Filter by session time if specified
                if session_start and session_end:
                    df = engine._filter_by_session_time(df, session_start, session_end)

                # Find all unique times that appear in all analysis days
                opportunities = find_consistent_time_patterns(df, pair, analysis_days, min_confidence, engine)

                if opportunities:
                    all_opportunities.extend(opportunities)
                    print_colored(f"   ✅ Found {len(opportunities)} opportunities for {pair}", "SUCCESS")
                else:
                    print_colored(f"   ⚪ No high-confidence patterns found for {pair}", "WARNING")

                # Small delay to avoid overwhelming the API
                time.sleep(0.5)

            except Exception as e:
                print_colored(f"   ❌ Error analyzing {pair}: {str(e)}", "ERROR")

        # Display all opportunities
        display_pattern_opportunities(all_opportunities, analysis_days, min_confidence)

    except Exception as e:
        print_colored(f"❌ Error in full day scan: {str(e)}", "ERROR")

def find_consistent_time_patterns(df, pair, analysis_days, min_confidence, engine):
    """Find all times with consistent patterns"""
    opportunities = []

    try:
        # Group data by date and time
        df['date'] = pd.to_datetime(df.index).date
        df['time_only'] = pd.to_datetime(df.index).time

        # Get all unique times
        unique_times = sorted(df['time_only'].unique())

        # Sample every 5 minutes to avoid too many checks (you can adjust this)
        sampled_times = unique_times[::5]  # Every 5th time point

        print_colored(f"   🔍 Checking {len(sampled_times)} time points...", "INFO")

        for time_obj in sampled_times:
            try:
                # Get candles for this time across all days
                time_candles = []
                unique_dates = sorted(df['date'].unique(), reverse=True)[:analysis_days]

                for date in unique_dates:
                    day_data = df[(df['date'] == date) & (df['time_only'] == time_obj)]
                    if len(day_data) > 0:
                        time_candles.append(day_data.iloc[0])

                if len(time_candles) >= max(3, analysis_days - 1):  # Need at least 3 days or analysis_days-1
                    # Analyze pattern for this time
                    pattern_analysis = engine._analyze_pattern_consistency(time_candles)

                    if pattern_analysis['consistency'] >= 0.6:  # At least 60% consistency
                        # Apply advanced filters using latest candle
                        current_candle = df.iloc[-1]
                        filter_scores = engine._apply_advanced_filters(df, current_candle, pattern_analysis)

                        # Calculate final confidence
                        time_str = time_obj.strftime('%H:%M')
                        final_result = engine._calculate_final_signal(pattern_analysis, filter_scores, pair, time_str)

                        if final_result['confidence'] >= min_confidence:
                            opportunities.append({
                                'pair': pair,
                                'time': time_str,
                                'signal': final_result['signal'],
                                'confidence': final_result['confidence'],
                                'pattern_type': pattern_analysis['pattern_type'],
                                'consistency': pattern_analysis['consistency'],
                                'filter_scores': filter_scores,
                                'days_analyzed': len(time_candles)
                            })

            except Exception as e:
                continue  # Skip this time if there's an error

        return opportunities

    except Exception as e:
        print_colored(f"❌ Error finding patterns: {str(e)}", "ERROR")
        return []

def display_pattern_opportunities(opportunities, analysis_days, min_confidence):
    """Display all found pattern opportunities"""
    try:
        print_header(f"🎯 PATTERN TRADING OPPORTUNITIES FOUND")

        if not opportunities:
            print_colored("⚪ No high-confidence pattern opportunities found", "WARNING")
            print_colored(f"   Try lowering the minimum confidence threshold (currently {min_confidence*100:.0f}%)", "INFO")
            return

        # Sort opportunities by confidence (highest first)
        opportunities.sort(key=lambda x: x['confidence'], reverse=True)

        print_colored(f"✅ Found {len(opportunities)} trading opportunities!", "SUCCESS", bold=True)
        print_colored(f"📊 Analysis Period: {analysis_days} days | Min Confidence: {min_confidence*100:.0f}%", "INFO")
        print()

        # Print detailed table header
        header_line = (
            f"💱 {'PAIR':<12} | "
            f"🕐 {'TIME':<8} | "
            f"📈📉 {'SIGNAL':<8} | "
            f"🎯 {'CONF%':<6} | "
            f"📊 {'PATTERN':<8} | "
            f"🔄 {'TREND':<6} | "
            f"🏗️ {'STRUCT':<6} | "
            f"📈 {'VOLAT':<6} | "
            f"⚡ {'MOMENT':<6}"
        )

        separator_line = "=" * 95

        print_colored(separator_line, "HEADER")
        print_colored(header_line, "HEADER", bold=True)
        print_colored(separator_line, "HEADER")

        # Display each opportunity
        for opp in opportunities:
            signal_color = "BUY" if opp['signal'] == 'BUY' else "SELL"
            signal_display = f"📈 {opp['signal']}" if opp['signal'] == 'BUY' else f"📉 {opp['signal']}"

            row_line = (
                f"💱 {opp['pair']:<12} | "
                f"🕐 {opp['time']:<8} | "
                f"{signal_display:<12} | "
                f"🎯 {opp['confidence']*100:.1f}% | "
                f"📊 {opp['pattern_type']:<8} | "
                f"🔄 {opp['filter_scores']['trend']*100:.0f}% | "
                f"🏗️ {opp['filter_scores']['structure']*100:.0f}% | "
                f"📈 {opp['filter_scores']['volatility']*100:.0f}% | "
                f"⚡ {opp['filter_scores']['momentum']*100:.0f}%"
            )

            print_colored(row_line, signal_color)

        print_colored(separator_line, "HEADER")

        # Summary by signal type
        buy_signals = [opp for opp in opportunities if opp['signal'] == 'BUY']
        sell_signals = [opp for opp in opportunities if opp['signal'] == 'SELL']

        print_colored(f"\n📊 OPPORTUNITY SUMMARY:", "HEADER", bold=True)
        print_colored(f"   📈 BUY Opportunities: {len(buy_signals)}", "BUY")
        print_colored(f"   📉 SELL Opportunities: {len(sell_signals)}", "SELL")
        print_colored(f"   🎯 Average Confidence: {sum(opp['confidence'] for opp in opportunities)/len(opportunities)*100:.1f}%", "SUCCESS")

        # Show top 3 opportunities
        if len(opportunities) >= 3:
            print_colored(f"\n🏆 TOP 3 OPPORTUNITIES:", "SUCCESS", bold=True)
            for i, opp in enumerate(opportunities[:3], 1):
                signal_emoji = "📈" if opp['signal'] == 'BUY' else "📉"
                print_colored(f"   {i}. {signal_emoji} {opp['pair']} at {opp['time']} - {opp['confidence']*100:.1f}% confidence", "SUCCESS")

        print_colored(f"\n💡 TRADING PLAN:", "INFO", bold=True)
        print_colored(f"   • Monitor these times for the specified signals", "INFO")
        print_colored(f"   • Higher confidence = stronger pattern", "INFO")
        print_colored(f"   • Consider market conditions before trading", "INFO")

    except Exception as e:
        print_colored(f"❌ Error displaying opportunities: {str(e)}", "ERROR")

def start_advanced_signals_analysis(selected_pairs=None):
    """Start advanced signals analysis"""
    try:
        # Get analysis type choice
        choice = get_user_analysis_choice()
        
        if choice == '3':
            return
        
        # Select currency pairs if not provided
        if selected_pairs is None:
            selected_pairs = select_currency_pairs()
        
        print()
        print_colored(f"🚀 Starting Advanced Pattern Analysis for {len(selected_pairs)} pairs", "SUCCESS", bold=True)
        print()
        
        if choice == '1':
            # Single time analysis
            bot = AdvancedSignalsBot(selected_pairs)
            
            if bot.setup_analysis_parameters():
                print_colored("🔍 Performing pattern analysis...", "INFO")
                bot.analyze_patterns()
            else:
                print_colored("❌ Failed to setup analysis parameters", "ERROR")
        
        elif choice == '2':
            # Full day pattern scan
            start_full_day_pattern_scan(selected_pairs)
        
    except KeyboardInterrupt:
        print_colored("\n⚠️ Analysis interrupted by user", "WARNING")
    except Exception as e:
        print_colored(f"❌ Error in advanced signals analysis: {str(e)}", "ERROR")

def main(selected_pairs=None):
    """Main function for advanced signals bot"""
    try:
        start_advanced_signals_analysis(selected_pairs)
    except Exception as e:
        print_colored(f"❌ Fatal error in advanced signals bot: {str(e)}", "ERROR")

if __name__ == "__main__":
    main()
