#!/usr/bin/env python3
"""
Trading Bot Configuration
"""

# Oanda API Configuration
OANDA_CONFIG = {
    "ACCESS_TOKEN": "69091c014738dc994e79ba405a77eb84-a46c00ab9195105c2f9a66331e92e9c1",
    "ACCOUNT_ID": "101-004-********-001",
    "BASE_URL": "https://api-fxpractice.oanda.com",  # Practice environment
    "ENVIRONMENT": "practice"  # practice or live
}

# Currency pairs to monitor
CURRENCY_PAIRS = [
    "EUR_USD", "GBP_USD", "USD_JPY", "AUD_USD", "USD_CAD",
    "AUD_CAD", "EUR_JPY", "GBP_JPY", "USD_CHF", "EUR_GBP"
]

# Trading Configuration
TRADING_CONFIG = {
    "CANDLE_INTERVAL": "M1",  # 1-minute candles
    "FETCH_INTERVAL": 58,     # Fetch data 2 seconds before next candle (58 seconds)
    "LOOKBACK_CANDLES": 100,  # Number of historical candles to fetch for analysis
    "MIN_CONFIDENCE": 0.6,    # Minimum confidence threshold for signals
}

# Strategy Configuration
STRATEGY_CONFIG = {
    "S1": {
        "name": "Breakout with Volume",
        "file": "output_signals_S1.csv",
        "signal_column": "strategy1_signal",
        "enabled": True
    },
    "S2": {
        "name": "Order Block Strategy", 
        "file": "output_signals_S2.csv",
        "signal_column": "strategy2_signal",
        "enabled": True
    },
    "S3": {
        "name": "Support/Resistance Rejection",
        "file": "output_signals_S3.csv", 
        "signal_column": "strategy3_signal",
        "enabled": True
    },
    "S4": {
        "name": "Trendline Break with Rejection",
        "file": "output_signals_S4.csv",
        "signal_column": "strategy4_signal", 
        "enabled": True
    }
}

# Model Configuration
MODEL_CONFIG = {
    "MODEL_PATH": "trained_models/ensemble_four_strategy_model.pkl",
    "SCALER_PATH": "trained_models/four_strategy_scaler.pkl",
    "LABEL_ENCODER_PATH": "trained_models/four_strategy_label_encoder.pkl",
    "FEATURES_PATH": "trained_models/four_strategy_features.pkl"
}

# Display Configuration
DISPLAY_CONFIG = {
    "COLORS": {
        "BUY": "\033[92m",      # Green
        "SELL": "\033[91m",     # Red
        "HOLD": "\033[93m",     # Yellow
        "INFO": "\033[94m",     # Blue
        "SUCCESS": "\033[92m",  # Green
        "WARNING": "\033[93m",  # Yellow
        "ERROR": "\033[91m",    # Red
        "RESET": "\033[0m",     # Reset
        "BOLD": "\033[1m",      # Bold
        "HEADER": "\033[95m",   # Magenta
        "DATE": "\033[96m",     # Cyan
        "TIME": "\033[97m",     # White
        "PAIR": "\033[94m",     # Blue
        "PRICE": "\033[93m",    # Yellow
        "CONFIDENCE": "\033[92m", # Green
        "STRATEGY": "\033[95m", # Magenta
        "NO_SIGNAL": "\033[90m" # Dark Gray
    },
    "ICONS": {
        "DATE": "📅",
        "TIME": "🕐",
        "PAIR": "💱",
        "PRICE": "💰",
        "BUY": "📈",
        "SELL": "📉",
        "CONFIDENCE": "🎯",
        "STRATEGY": "🔧",
        "NO_SIGNAL": "❌",
        "ML": "🧠",
        "RULE": "📊"
    },
    "TABLE_WIDTH": 80,
    "DECIMAL_PLACES": 5,
    "SIGNAL_TABLE": {
        "COLUMN_WIDTHS": [12, 10, 12, 12, 10, 12, 15],
        "HEADERS": ["Date", "Time", "Pair", "Price", "Signal", "Confidence", "Strategy"],
        "TOTAL_WIDTH": 95
    }
}

# Advanced Pattern Recognition Configuration
ADVANCED_PATTERN_CONFIG = {
    "DEFAULT_ANALYSIS_DAYS": 5,
    "MIN_PATTERN_CONSISTENCY": 0.6,  # 60% consistency required
    "MIN_CANDLE_BODY_RATIO": 0.3,    # Body must be 30% of total range
    "VOLATILITY_THRESHOLD": 1.2,      # ATR multiplier
    "TREND_LOOKBACK": 20,             # Candles for trend analysis
    "STRUCTURE_LOOKBACK": 50,         # Candles for support/resistance
    "RSI_OVERBOUGHT": 70,
    "RSI_OVERSOLD": 30,
    "MACD_SIGNAL_THRESHOLD": 0.0001,
    "SCORING_WEIGHTS": {
        "PATTERN_CONSISTENCY": 40,     # Base pattern score weight
        "TREND_ALIGNMENT": 20,         # Trend confirmation weight
        "STRUCTURE_SUPPORT": 15,       # Support/resistance weight
        "VOLATILITY_FILTER": 10,       # Volatility confirmation weight
        "MOMENTUM_FILTER": 10,         # RSI/MACD weight
        "CANDLE_STRENGTH": 5           # Candle body strength weight
    },
    "TIME_FORMATS": {
        "INPUT_FORMAT": "%H:%M",
        "DISPLAY_FORMAT": "%H:%M:%S"
    }
}

# Backtesting Configuration
BACKTEST_CONFIG = {
    "DEFAULT_CANDLES": 1000,
    "MAX_CANDLES": 10000,
    "WIN_THRESHOLD": 0.0001,  # Minimum price movement to consider a win (1 pip for most pairs)
    "RESULTS_PRECISION": 4
}
