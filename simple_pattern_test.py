#!/usr/bin/env python3
"""
Simple pattern test to see what's happening
"""

import sys
import os
sys.path.append(os.getcwd())

from utils import fetch_live_candles, print_colored
from advanced_pattern_engine import AdvancedPatternEngine
import pandas as pd

print_colored("🔍 SIMPLE PATTERN TEST", "HEADER", bold=True)

# Get data
print_colored("📊 Fetching EUR_USD data...", "INFO")
df = fetch_live_candles("EUR_USD", 1000)

if df is None:
    print_colored("❌ No data", "ERROR")
    exit()

print_colored(f"✅ Got {len(df)} candles", "SUCCESS")

# Add time info
df['date'] = pd.to_datetime(df.index).date
df['time_only'] = pd.to_datetime(df.index).time

# Get unique dates and times
unique_dates = sorted(df['date'].unique(), reverse=True)[:3]
unique_times = sorted(df['time_only'].unique())

print_colored(f"📅 Dates: {len(unique_dates)} - {unique_dates}", "INFO")
print_colored(f"⏰ Times: {len(unique_times)} (showing first 5: {unique_times[:5]})", "INFO")

# Test one specific time
test_time = unique_times[len(unique_times)//2]  # Middle time
print_colored(f"\n🔍 Testing time: {test_time}", "INFO")

# Get candles for this time
time_candles = []
for date in unique_dates:
    day_data = df[(df['date'] == date) & (df['time_only'] == test_time)]
    if len(day_data) > 0:
        candle = day_data.iloc[0]
        time_candles.append(candle)
        print_colored(f"   📅 {date}: Open={candle['open']:.5f}, Close={candle['close']:.5f}, Color={'🟢' if candle['close'] > candle['open'] else '🔴'}", "INFO")

print_colored(f"\n📊 Found {len(time_candles)} candles for analysis", "INFO")

if len(time_candles) >= 2:
    # Test pattern analysis
    engine = AdvancedPatternEngine()
    pattern_analysis = engine._analyze_pattern_consistency(time_candles)
    
    print_colored(f"📈 Pattern Analysis Results:", "SUCCESS")
    print_colored(f"   • Pattern Type: {pattern_analysis['pattern_type']}", "INFO")
    print_colored(f"   • Consistency: {pattern_analysis['consistency']*100:.1f}%", "INFO")
    print_colored(f"   • Signal: {pattern_analysis['signal']}", "INFO")
    print_colored(f"   • Buy Days: {pattern_analysis['buy_days']}", "INFO")
    print_colored(f"   • Sell Days: {pattern_analysis['sell_days']}", "INFO")
    print_colored(f"   • Total Days: {pattern_analysis['total_days']}", "INFO")
    
    if pattern_analysis['consistency'] >= 0.3:
        print_colored(f"✅ Pattern meets 30% threshold!", "SUCCESS")
        
        # Test full signal
        current_candle = df.iloc[-1]
        filter_scores = engine._apply_advanced_filters(df, current_candle, pattern_analysis)
        final_result = engine._calculate_final_signal(pattern_analysis, filter_scores, "EUR_USD", test_time.strftime('%H:%M'))
        
        print_colored(f"\n🎯 Final Signal Results:", "SUCCESS")
        print_colored(f"   • Signal: {final_result['signal']}", "INFO")
        print_colored(f"   • Confidence: {final_result['confidence']*100:.1f}%", "INFO")
        
        if final_result['confidence'] >= 0.2:
            print_colored(f"🎉 SIGNAL GENERATED! This would appear in results!", "SUCCESS", bold=True)
        else:
            print_colored(f"❌ Confidence too low (need ≥20%)", "WARNING")
    else:
        print_colored(f"❌ Pattern consistency too low (need ≥30%)", "WARNING")
else:
    print_colored(f"❌ Not enough candles for analysis", "ERROR")

print_colored(f"\n✅ Test complete!", "SUCCESS")
