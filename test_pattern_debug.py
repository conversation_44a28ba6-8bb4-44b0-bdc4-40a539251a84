#!/usr/bin/env python3
"""
Direct test of pattern finding with maximum debug
"""

import sys
import os
sys.path.append(os.getcwd())

def test_pattern_finding():
    """Test pattern finding with debug"""
    try:
        from utils import fetch_live_candles, print_colored
        from advanced_pattern_engine import AdvancedPatternEngine
        import pandas as pd
        
        print_colored("🧪 DIRECT PATTERN FINDING TEST", "HEADER", bold=True)
        print()
        
        # Fetch data
        pair = "EUR_USD"
        print_colored(f"📊 Fetching data for {pair}...", "INFO")
        df = fetch_live_candles(pair, 3000)
        
        if df is None or len(df) == 0:
            print_colored("❌ No data received", "ERROR")
            return
        
        print_colored(f"✅ Got {len(df)} candles", "SUCCESS")
        
        # Add time information
        df['date'] = pd.to_datetime(df.index).date
        df['time_only'] = pd.to_datetime(df.index).time
        
        print_colored(f"📅 Date range: {df['date'].min()} to {df['date'].max()}", "INFO")
        print_colored(f"🕐 Time range: {df['time_only'].min()} to {df['time_only'].max()}", "INFO")
        
        # Get unique times and dates
        unique_times = sorted(df['time_only'].unique())
        unique_dates = sorted(df['date'].unique(), reverse=True)[:3]  # Last 3 days
        
        print_colored(f"⏰ Unique times: {len(unique_times)}", "INFO")
        print_colored(f"📅 Unique dates: {len(unique_dates)}", "INFO")
        
        # Test pattern engine
        engine = AdvancedPatternEngine()
        
        # Test a few specific times
        test_times = unique_times[::100][:10]  # Every 100th time, max 10
        
        print_colored(f"\n🔍 Testing {len(test_times)} specific times...", "INFO")
        
        patterns_found = 0
        
        for time_obj in test_times:
            time_str = time_obj.strftime('%H:%M')
            print_colored(f"\n   ⏰ Testing time: {time_str}", "INFO")
            
            # Get candles for this time across days
            time_candles = []
            for date in unique_dates:
                day_data = df[(df['date'] == date) & (df['time_only'] == time_obj)]
                if len(day_data) > 0:
                    time_candles.append(day_data.iloc[0])
            
            print_colored(f"      📊 Found {len(time_candles)} candles across {len(unique_dates)} days", "INFO")
            
            if len(time_candles) >= 2:
                # Analyze pattern
                pattern_analysis = engine._analyze_pattern_consistency(time_candles)
                
                print_colored(f"      📈 Pattern: {pattern_analysis['pattern_type']}", "INFO")
                print_colored(f"      🎯 Consistency: {pattern_analysis['consistency']*100:.1f}%", "INFO")
                print_colored(f"      📊 Signal: {pattern_analysis['signal']}", "INFO")
                
                if pattern_analysis['consistency'] >= 0.3:  # 30% threshold
                    patterns_found += 1
                    print_colored(f"      ✅ Pattern meets 30% threshold!", "SUCCESS")
                    
                    # Test full signal calculation
                    current_candle = df.iloc[-1]
                    filter_scores = engine._apply_advanced_filters(df, current_candle, pattern_analysis)
                    final_result = engine._calculate_final_signal(pattern_analysis, filter_scores, pair, time_str)
                    
                    print_colored(f"      💡 Final confidence: {final_result['confidence']*100:.1f}%", "SUCCESS")
                    print_colored(f"      🎯 Final signal: {final_result['signal']}", "SUCCESS")
                    
                    if final_result['confidence'] >= 0.2:  # 20% threshold
                        print_colored(f"      🎉 SIGNAL WOULD BE GENERATED!", "SUCCESS", bold=True)
                else:
                    print_colored(f"      ❌ Pattern below 30% threshold", "WARNING")
            else:
                print_colored(f"      ⚠️ Not enough candles for pattern analysis", "WARNING")
        
        print_colored(f"\n📊 SUMMARY:", "HEADER", bold=True)
        print_colored(f"   • Times tested: {len(test_times)}", "INFO")
        print_colored(f"   • Patterns found (≥30%): {patterns_found}", "SUCCESS" if patterns_found > 0 else "WARNING")
        
        if patterns_found == 0:
            print_colored(f"\n💡 SUGGESTIONS:", "INFO", bold=True)
            print_colored(f"   • Try different time periods", "INFO")
            print_colored(f"   • Market might be ranging (no clear patterns)", "INFO")
            print_colored(f"   • Consider using more days of data", "INFO")
        
    except Exception as e:
        print_colored(f"❌ Test failed: {str(e)}", "ERROR")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_pattern_finding()
