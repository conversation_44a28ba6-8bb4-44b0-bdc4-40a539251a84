#!/usr/bin/env python3
"""
Utility functions for the trading bot
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import requests
import json
from config import OANDA_CONFIG, DISPLAY_CONFIG, CURRENCY_PAIRS

def get_color(color_name):
    """Get color code from config"""
    return DISPLAY_CONFIG["COLORS"].get(color_name, "")

def print_colored(text, color="INFO", bold=False):
    """Print colored text"""
    color_code = get_color(color)
    bold_code = get_color("BOLD") if bold else ""
    reset_code = get_color("RESET")
    print(f"{bold_code}{color_code}{text}{reset_code}")

def print_header(text, width=None):
    """Print a formatted header"""
    if width is None:
        width = DISPLAY_CONFIG["TABLE_WIDTH"]
    
    print_colored("=" * width, "HEADER", bold=True)
    print_colored(text.center(width), "HEADER", bold=True)
    print_colored("=" * width, "HEADER", bold=True)

def print_table_row(columns, widths=None, colors=None):
    """Print a formatted table row"""
    if widths is None:
        widths = [15] * len(columns)
    if colors is None:
        colors = ["INFO"] * len(columns)

    row = ""
    for i, (col, width, color) in enumerate(zip(columns, widths, colors)):
        color_code = get_color(color)
        reset_code = get_color("RESET")
        row += f"{color_code}{str(col):<{width}}{reset_code}"

    print(row)

def get_icon(icon_name):
    """Get icon from config"""
    return DISPLAY_CONFIG["ICONS"].get(icon_name, "")

def print_signal_table_header():
    """Print the signal table header with icons and colors in pipe-separated format"""
    # Print header with proper spacing and pipe separators
    header_line = (
        f"💱 {'PAIR':<15} | "
        f"📅 {'DATE':<15} | "
        f"🕐 {'TIME':<13} | "
        f"📈📉 {'DIRECTION':<11} | "
        f"🎯 {'CONFIDENCE':<11} | "
        f"💰 {'PRICE':<13} | "
        f"🔧 {'STRATEGY':<10}"
    )

    # Print separator line
    separator_line = "=" * 120

    print_colored(separator_line, "HEADER")
    print_colored(header_line, "HEADER", bold=True)
    print_colored(separator_line, "HEADER")

def print_signal_row(date, time, pair, price, signal=None, confidence=None, strategy=None, is_no_signal=False):
    """Print a single signal row in pipe-separated format"""
    # Prepare display values
    if is_no_signal:
        direction_display = "⚪ NO SIGNAL"
        confidence_display = "🎯 -"
        strategy_display = "🔧 -"
        direction_color = "NO_SIGNAL"
    else:
        # Determine signal icon and display
        if signal == "BUY":
            direction_display = "📈 BUY"
            direction_color = "BUY"
        elif signal == "SELL":
            direction_display = "📉 SELL"
            direction_color = "SELL"
        else:
            direction_display = "⚪ NO SIGNAL"
            direction_color = "NO_SIGNAL"

        confidence_display = f"🎯 {confidence}" if confidence else "🎯 -"
        strategy_display = f"🔧 {strategy}" if strategy else "🔧 -"

    # Create the formatted row with pipe separators
    pair_colored = f"{get_color('PAIR')}💱 {pair:<15}{get_color('RESET')}"
    date_colored = f"{get_color('DATE')}📅 {date:<15}{get_color('RESET')}"
    time_colored = f"{get_color('TIME')}🕐 {time:<13}{get_color('RESET')}"
    direction_colored = f"{get_color(direction_color)}{direction_display:<13}{get_color('RESET')}"
    confidence_colored = f"{get_color('CONFIDENCE')}{confidence_display:<13}{get_color('RESET')}"
    price_colored = f"{get_color('PRICE')}💰 {price:<13}{get_color('RESET')}"
    strategy_colored = f"{get_color('STRATEGY')}{strategy_display:<12}{get_color('RESET')}"

    row_line = (
        f"{pair_colored} | "
        f"{date_colored} | "
        f"{time_colored} | "
        f"{direction_colored} | "
        f"{confidence_colored} | "
        f"{price_colored} | "
        f"{strategy_colored}"
    )

    print(row_line)

def print_ml_signal_row(date, time, pair, price, final_signal=None, final_confidence=None,
                       ml_signal=None, ml_confidence=None, rule_signal=None, rule_confidence=None,
                       strategy=None, is_no_signal=False):
    """Print a single ML signal row in pipe-separated format"""
    # Prepare display values
    if is_no_signal:
        final_display = "⚪ NO SIGNAL"
        final_conf_display = "🎯 -"
        ml_display = "🧠 -"
        ml_conf_display = "🎯 -"
        rule_display = "📊 -"
        strategy_display = "🔧 -"
        final_color = "NO_SIGNAL"
        ml_color = "NO_SIGNAL"
        rule_color = "NO_SIGNAL"
    else:
        # Determine final signal display
        if final_signal == "BUY":
            final_display = "📈 BUY"
            final_color = "BUY"
        elif final_signal == "SELL":
            final_display = "📉 SELL"
            final_color = "SELL"
        else:
            final_display = "⚪ NO SIGNAL"
            final_color = "NO_SIGNAL"

        # Determine ML signal display
        if ml_signal == "BUY":
            ml_display = "🧠 BUY"
            ml_color = "BUY"
        elif ml_signal == "SELL":
            ml_display = "🧠 SELL"
            ml_color = "SELL"
        else:
            ml_display = "🧠 HOLD"
            ml_color = "HOLD"

        # Determine rule signal display
        if rule_signal == "BUY":
            rule_display = "📊 BUY"
            rule_color = "BUY"
        elif rule_signal == "SELL":
            rule_display = "📊 SELL"
            rule_color = "SELL"
        else:
            rule_display = "📊 HOLD"
            rule_color = "HOLD"

        final_conf_display = f"🎯 {final_confidence}" if final_confidence else "🎯 -"
        ml_conf_display = f"🎯 {ml_confidence}" if ml_confidence else "🎯 -"
        strategy_display = f"🔧 {strategy}" if strategy else "🔧 -"

    # Create the formatted row with pipe separators (reduced spacing)
    pair_colored = f"{get_color('PAIR')}💱 {pair:<10}{get_color('RESET')}"
    date_colored = f"{get_color('DATE')}📅 {date:<10}{get_color('RESET')}"
    time_colored = f"{get_color('TIME')}🕐 {time:<8}{get_color('RESET')}"
    final_colored = f"{get_color(final_color)}{final_display:<10}{get_color('RESET')}"
    final_conf_colored = f"{get_color('CONFIDENCE')}{final_conf_display:<8}{get_color('RESET')}"
    ml_colored = f"{get_color(ml_color)}{ml_display:<8}{get_color('RESET')}"
    ml_conf_colored = f"{get_color('CONFIDENCE')}{ml_conf_display:<8}{get_color('RESET')}"
    rule_colored = f"{get_color(rule_color)}{rule_display:<8}{get_color('RESET')}"
    price_colored = f"{get_color('PRICE')}💰 {price:<10}{get_color('RESET')}"
    strategy_colored = f"{get_color('STRATEGY')}{strategy_display:<10}{get_color('RESET')}"

    row_line = (
        f"{pair_colored} | "
        f"{date_colored} | "
        f"{time_colored} | "
        f"{final_colored} | "
        f"{final_conf_colored} | "
        f"{ml_colored} | "
        f"{ml_conf_colored} | "
        f"{rule_colored} | "
        f"{price_colored} | "
        f"{strategy_colored}"
    )

    print(row_line)

def print_ml_signal_table_header():
    """Print the ML signal table header with icons and colors in pipe-separated format"""
    # Print header with reduced spacing for ML display
    header_line = (
        f"💱 {'PAIR':<10} | "
        f"📅 {'DATE':<10} | "
        f"🕐 {'TIME':<8} | "
        f"📈📉 {'FINAL':<8} | "
        f"🎯 {'F.CNF':<6} | "
        f"🧠 {'ML':<6} | "
        f"🎯 {'M.CNF':<6} | "
        f"📊 {'RULE':<6} | "
        f"💰 {'PRICE':<10} | "
        f"🔧 {'STRAT':<8}"
    )

    # Print separator line
    separator_line = "=" * 110

    print_colored(separator_line, "HEADER")
    print_colored(header_line, "HEADER", bold=True)
    print_colored(separator_line, "HEADER")

def format_price(price, decimal_places=None):
    """Format price with appropriate decimal places"""
    if decimal_places is None:
        decimal_places = DISPLAY_CONFIG["DECIMAL_PLACES"]
    return f"{float(price):.{decimal_places}f}"

def format_percentage(value):
    """Format percentage value"""
    return f"{float(value):.2f}%"

def get_oanda_headers():
    """Get headers for Oanda API requests"""
    return {
        "Authorization": f"Bearer {OANDA_CONFIG['ACCESS_TOKEN']}",
        "Content-Type": "application/json"
    }

def fetch_live_candles(instrument, count=100):
    """Fetch live candle data from Oanda API"""
    headers = get_oanda_headers()
    
    try:
        url = f"{OANDA_CONFIG['BASE_URL']}/v3/instruments/{instrument}/candles"
        params = {
            "count": count,
            "granularity": "M1",
            "price": "MBA"
        }
        
        response = requests.get(url, headers=headers, params=params)

        if response.status_code == 200:
            data = response.json()
            return process_candle_data(data['candles'])
        else:
            print_colored(f"❌ Failed to fetch data for {instrument}: {response.status_code}", "ERROR")
            if response.status_code == 400:
                try:
                    error_data = response.json()
                    print_colored(f"   API Error: {error_data.get('errorMessage', 'Bad Request')}", "ERROR")
                    print_colored(f"   Requested {count} candles - try reducing the number", "WARNING")
                except:
                    print_colored(f"   Bad Request - possibly too many candles requested ({count})", "WARNING")
            return None
            
    except Exception as e:
        print_colored(f"❌ Error fetching data for {instrument}: {str(e)}", "ERROR")
        return None

def process_candle_data(candles):
    """Process raw candle data into DataFrame"""
    processed_data = []
    
    for candle in candles:
        mid = candle['mid']
        
        # Convert time to datetime
        time_str = candle['time'][:19].replace('T', ' ')
        
        processed_data.append({
            'time': time_str,
            'open': float(mid['o']),
            'high': float(mid['h']),
            'low': float(mid['l']),
            'close': float(mid['c']),
            'volume': int(candle['volume']),
            'complete': candle['complete']
        })
    
    df = pd.DataFrame(processed_data)

    # Convert time column to datetime and set as index
    df['time'] = pd.to_datetime(df['time'])
    df.set_index('time', inplace=True)

    # Add technical indicators
    df = add_technical_indicators(df)

    return df

def add_technical_indicators(df):
    """Add technical indicators to the DataFrame"""
    # RSI
    df['rsi'] = calculate_rsi(df['close'])
    
    # MACD
    df['macd'], df['macd_signal'] = calculate_macd(df['close'])
    
    # Moving averages
    df['sma_20'] = df['close'].rolling(window=20).mean()
    df['ema_12'] = df['close'].ewm(span=12).mean()
    df['ema_26'] = df['close'].ewm(span=26).mean()
    
    # Bollinger Bands
    df['bb_upper'], df['bb_lower'] = calculate_bollinger_bands(df['close'])
    
    # Volume indicators
    df['volume_sma'] = df['volume'].rolling(window=20).mean()
    
    return df

def calculate_rsi(prices, period=14):
    """Calculate RSI indicator"""
    delta = prices.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    return rsi

def calculate_macd(prices, fast=12, slow=26, signal=9):
    """Calculate MACD indicator"""
    ema_fast = prices.ewm(span=fast).mean()
    ema_slow = prices.ewm(span=slow).mean()
    macd = ema_fast - ema_slow
    macd_signal = macd.ewm(span=signal).mean()
    return macd, macd_signal

def calculate_bollinger_bands(prices, period=20, std_dev=2):
    """Calculate Bollinger Bands"""
    sma = prices.rolling(window=period).mean()
    std = prices.rolling(window=period).std()
    upper_band = sma + (std * std_dev)
    lower_band = sma - (std * std_dev)
    return upper_band, lower_band

def get_current_time_info():
    """Get current time and calculate seconds until next minute"""
    now = datetime.now()
    seconds_to_next_minute = 60 - now.second
    
    return {
        'current_time': now,
        'time_str': now.strftime('%Y-%m-%d %H:%M:%S'),
        'seconds_to_next_minute': seconds_to_next_minute
    }

def validate_pair(pair):
    """Validate if currency pair is supported"""
    return pair in CURRENCY_PAIRS

def select_currency_pairs():
    """Allow user to select currency pairs for trading/analysis"""
    print_colored("\n💱 Available currency pairs:", "INFO", bold=True)
    for i, pair in enumerate(CURRENCY_PAIRS, 1):
        print_colored(f"   {i}. {pair}", "INFO")

    while True:
        print_colored("\nEnter pair numbers (comma-separated) or 'all' for all pairs:", "SUCCESS")
        pairs_input = input("   Pairs: ").strip().lower()

        if pairs_input == 'all':
            selected_pairs = CURRENCY_PAIRS.copy()
            break
        else:
            try:
                pair_indices = [int(x.strip()) for x in pairs_input.split(',')]
                selected_pairs = []

                for idx in pair_indices:
                    if 1 <= idx <= len(CURRENCY_PAIRS):
                        selected_pairs.append(CURRENCY_PAIRS[idx-1])
                    else:
                        print_colored(f"❌ Invalid pair number: {idx}", "ERROR")
                        raise ValueError

                if selected_pairs:
                    break
                else:
                    print_colored("❌ No valid pairs selected", "ERROR")

            except ValueError:
                print_colored("❌ Please enter valid pair numbers", "ERROR")

    print_colored(f"\n✅ Selected pairs: {', '.join(selected_pairs)}", "SUCCESS")
    return selected_pairs

def print_advanced_signal_table_header():
    """Print the advanced signal table header"""
    header_line = (
        f"💱 {'PAIR':<12} | "
        f"🕐 {'TIME':<8} | "
        f"📈📉 {'SIGNAL':<8} | "
        f"🎯 {'CONF%':<6} | "
        f"📊 {'PATTERN':<8} | "
        f"🔄 {'TREND':<6} | "
        f"🏗️ {'STRUCT':<6} | "
        f"📈 {'VOLAT':<6} | "
        f"⚡ {'MOMENT':<6} | "
        f"💪 {'STRENGTH':<8}"
    )

    separator_line = "=" * 110

    print_colored(separator_line, "HEADER")
    print_colored(header_line, "HEADER", bold=True)
    print_colored(separator_line, "HEADER")

def print_advanced_signal_row(pair, target_time, signal, confidence, pattern_info, filter_scores):
    """Print a single advanced signal row"""
    try:
        # Format signal display
        if signal == 'HOLD':
            signal_display = "⚪ HOLD"
            signal_color = "NO_SIGNAL"
            conf_display = "-"
        elif signal == 'BUY':
            signal_display = "📈 BUY"
            signal_color = "BUY"
            conf_display = f"{confidence*100:.1f}%"
        else:  # SELL
            signal_display = "📉 SELL"
            signal_color = "SELL"
            conf_display = f"{confidence*100:.1f}%"

        # Pattern info
        pattern_display = pattern_info.get('pattern_type', '-')

        # Filter scores (convert to percentages)
        trend_score = f"{filter_scores.get('trend', 0)*100:.0f}%"
        struct_score = f"{filter_scores.get('structure', 0)*100:.0f}%"
        volat_score = f"{filter_scores.get('volatility', 0)*100:.0f}%"
        moment_score = f"{filter_scores.get('momentum', 0)*100:.0f}%"
        strength_score = f"{filter_scores.get('candle_strength', 0)*100:.0f}%"

        # Create the formatted row
        row_line = (
            f"💱 {pair:<12} | "
            f"🕐 {target_time:<8} | "
            f"{signal_display:<12} | "
            f"🎯 {conf_display:<6} | "
            f"📊 {pattern_display:<8} | "
            f"🔄 {trend_score:<6} | "
            f"🏗️ {struct_score:<6} | "
            f"📈 {volat_score:<6} | "
            f"⚡ {moment_score:<6} | "
            f"💪 {strength_score:<8}"
        )

        # Print with appropriate color
        print_colored(row_line, signal_color)

    except Exception as e:
        print_colored(f"❌ Error displaying advanced signal for {pair}: {str(e)}", "ERROR")

def format_signal_output(pair, signal_data):
    """Format signal output for display"""
    time_info = get_current_time_info()
    
    if signal_data['signal'] != 'HOLD':
        color = "BUY" if signal_data['signal'] == 'BUY' else "SELL"
        return {
            'date': time_info['current_time'].strftime('%Y-%m-%d'),
            'time': time_info['current_time'].strftime('%H:%M:%S'),
            'pair': pair,
            'direction': signal_data['signal'],
            'price': format_price(signal_data['price']),
            'confidence': format_percentage(signal_data['confidence'] * 100),
            'strategy': signal_data['strategy'],
            'color': color
        }
    else:
        return {
            'date': time_info['current_time'].strftime('%Y-%m-%d'),
            'time': time_info['current_time'].strftime('%H:%M:%S'),
            'pair': pair,
            'price': format_price(signal_data['price']),
            'message': 'No signal found',
            'color': 'HOLD'
        }
